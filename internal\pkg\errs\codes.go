package errs

// 文件模块错误码
const (
	CodeFileInvalidParams     = "FILE.INVALID_PARAMS"
	CodeFileUnsupportedType   = "FILE.UNSUPPORTED_TYPE"
	CodeFileNotFound          = "FILE.NOT_FOUND"
	CodeFileStorageError      = "FILE.STORAGE_ERROR"
	CodeFileGenerateURLError  = "FILE.GENERATE_URL_ERROR"
	CodeFileDeleteError       = "FILE.DELETE_ERROR"
)

// 用户模块错误码
const (
	CodeUserInvalidParams    = "USER.INVALID_PARAMS"
	CodeUserNotFound         = "USER.NOT_FOUND"
	CodeUserAlreadyExists    = "USER.ALREADY_EXISTS"
	CodeUserDisabled         = "USER.DISABLED"
	CodeUserEmailExists      = "USER.EMAIL_EXISTS"
)

// 认证模块错误码
const (
	CodeAuthUserNotFound     = "AUTH.USER_NOT_FOUND"
	CodeAuthInvalidPassword  = "AUTH.INVALID_PASSWORD"
	CodeAuthTokenExpired     = "AUTH.TOKEN_EXPIRED"
	CodeAuthTokenInvalid     = "AUTH.TOKEN_INVALID"
	CodeAuthPermissionDenied = "AUTH.PERMISSION_DENIED"
)

// 食物模块错误码
const (
	CodeFoodNotFound         = "FOOD.NOT_FOUND"
	CodeFoodInvalidParams    = "FOOD.INVALID_PARAMS"
	CodeFoodAlreadyExists    = "FOOD.ALREADY_EXISTS"
	CodeFoodCategoryNotFound = "FOOD.CATEGORY_NOT_FOUND"
)

// 系统错误码
const (
	CodeInternal             = "SYSTEM.INTERNAL_ERROR"
	CodeDatabaseError        = "SYSTEM.DATABASE_ERROR"
	CodeExternalServiceError = "SYSTEM.EXTERNAL_SERVICE_ERROR"
)
