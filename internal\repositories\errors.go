package repositories

import "fmt"

// Repository层自定义错误类型
// 这些错误类型在Repository层定义和使用，为上层提供有意义的错误信息

// UserNotFoundError 用户未找到错误
type UserNotFoundError struct {
	Field string // 查询字段名（如 "email", "username", "id"）
	Value string // 查询值
}

func (e *UserNotFoundError) Error() string {
	return fmt.Sprintf("user not found by %s=%s", e.Field, e.Value)
}

// UserExistsError 用户已存在错误
type UserExistsError struct {
	Field string // 冲突字段名
	Value string // 冲突值
}

func (e *UserExistsError) Error() string {
	return fmt.Sprintf("user already exists with %s=%s", e.Field, e.Value)
}

// DatabaseError 数据库操作错误
type DatabaseError struct {
	Operation string // 操作类型（如 "create", "update", "delete", "query"）
	Table     string // 表名
	Err       error  // 原始错误
}

func (e *DatabaseError) Error() string {
	return fmt.Sprintf("database %s operation failed on table %s: %v", e.Operation, e.Table, e.Err)
}

func (e *DatabaseError) Unwrap() error {
	return e.Err
}

// FoodNotFoundError 食物未找到错误
type FoodNotFoundError struct {
	Field string // 查询字段名（如 "id", "name"）
	Value string // 查询值
}

func (e *FoodNotFoundError) Error() string {
	return fmt.Sprintf("food not found by %s=%s", e.Field, e.Value)
}

// FoodExistsError 食物已存在错误
type FoodExistsError struct {
	Field string // 冲突字段名
	Value string // 冲突值
}

func (e *FoodExistsError) Error() string {
	return fmt.Sprintf("food already exists with %s=%s", e.Field, e.Value)
}

// FoodCategoryNotFoundError 食物分类未找到错误
type FoodCategoryNotFoundError struct {
	Field string // 查询字段名（如 "id", "name"）
	Value string // 查询值
}

func (e *FoodCategoryNotFoundError) Error() string {
	return fmt.Sprintf("food category not found by %s=%s", e.Field, e.Value)
}

// FoodCategoryExistsError 食物分类已存在错误
type FoodCategoryExistsError struct {
	Field string // 冲突字段名
	Value string // 冲突值
}

func (e *FoodCategoryExistsError) Error() string {
	return fmt.Sprintf("food category already exists with %s=%s", e.Field, e.Value)
}

// DietRecordNotFoundError 饮食记录未找到错误
type DietRecordNotFoundError struct {
	Field string // 查询字段名（如 "id", "user_id"）
	Value string // 查询值
}

func (e *DietRecordNotFoundError) Error() string {
	return fmt.Sprintf("diet record not found by %s=%s", e.Field, e.Value)
}

// DietRecordFoodNotFoundError 饮食记录食物明细未找到错误
type DietRecordFoodNotFoundError struct {
	Field string // 查询字段名（如 "id", "diet_record_id"）
	Value string // 查询值
}

func (e *DietRecordFoodNotFoundError) Error() string {
	return fmt.Sprintf("diet record food not found by %s=%s", e.Field, e.Value)
}

// NutritionAdviceNotFoundError 营养建议未找到错误
type NutritionAdviceNotFoundError struct {
	Field string // 查询字段名（如 "id"）
	Value interface{} // 查询值
}

func (e *NutritionAdviceNotFoundError) Error() string {
	return fmt.Sprintf("nutrition advice not found by %s=%v", e.Field, e.Value)
}

// NotFoundError 通用未找到错误
type NotFoundError struct {
	Resource string      // 资源名称
	Field    string      // 查询字段名
	Value    interface{} // 查询值
}

func (e *NotFoundError) Error() string {
	return fmt.Sprintf("%s not found by %s=%v", e.Resource, e.Field, e.Value)
}
