package file

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/service"
	"shikeyinxiang/internal/storage"
)

// FileLogic 文件服务业务逻辑实现
type FileLogic struct {
	r2Storage *storage.R2Storage
}

// NewFileLogic 创建文件服务业务逻辑实例
func NewFileLogic(r2Storage *storage.R2Storage) service.IFileService {
	return &FileLogic{
		r2Storage: r2Storage,
	}
}

// GenerateUploadPresignedURL 生成文件上传的预签名URL
func (f *FileLogic) GenerateUploadPresignedURL(ctx context.Context, id int64, fileType, contentType string, expiration int) (string, error) {
	// 参数验证
	if id <= 0 {
		return "", errs.New(errs.CodeFileInvalidParams, "id must be positive")
	}
	if fileType == "" {
		return "", errs.New(errs.CodeFileInvalidParams, "fileType cannot be empty")
	}
	if contentType == "" {
		return "", errs.New(errs.CodeFileInvalidParams, "contentType cannot be empty")
	}
	if expiration <= 0 {
		return "", errs.New(errs.CodeFileInvalidParams, "expiration must be positive")
	}

	// 调用R2Storage生成预签名URL（R2Storage会处理文件类型验证）
	presignedURL, fileName, err := f.r2Storage.GenerateUploadPresignedURL(id, fileType, contentType, time.Duration(expiration)*time.Minute)
	if err != nil {
		return "", errs.Wrap(errs.CodeFileGenerateURLError, "failed to generate upload presigned URL", err)
	}

	return presignedURL + ":::" + fileName, nil
}

// GenerateDownloadPresignedURL 生成文件下载的预签名URL
func (f *FileLogic) GenerateDownloadPresignedURL(ctx context.Context, fileName string, expiration int) (string, error) {
	// 参数验证
	if fileName == "" {
		return "", errs.New(errs.CodeFileInvalidParams, "fileName cannot be empty")
	}
	if expiration <= 0 {
		return "", errs.New(errs.CodeFileInvalidParams, "expiration must be positive")
	}

	// 调用R2Storage生成预签名URL
	presignedURL, err := f.r2Storage.GenerateDownloadPresignedURL(fileName, time.Duration(expiration)*time.Minute)
	if err != nil {
		return "", errs.Wrap(errs.CodeFileGenerateURLError, "failed to generate download presigned URL", err)
	}

	return presignedURL, nil
}

// DeleteFile 删除指定的文件
func (f *FileLogic) DeleteFile(ctx context.Context, fileName string) error {
	// 参数验证
	if fileName == "" {
		return errs.New(errs.CodeFileInvalidParams, "fileName cannot be empty")
	}

	// 调用R2Storage删除文件
	err := f.r2Storage.DeleteFile(fileName)
	if err != nil {
		return errs.Wrap(errs.CodeFileDeleteError, "failed to delete file", err)
	}

	return nil
}

// generateFileName 生成文件名
// Java版本格式：fileType + "/" + userId + "/" + UUID.randomUUID().toString().replaceAll("-", "") + "." + extension
func (f *FileLogic) generateFileName(userID int64, fileType, extension string) string {
	// 生成无连字符的UUID
	uuidStr := uuid.New().String()
	uuidWithoutDashes := strings.ReplaceAll(uuidStr, "-", "")

	return fmt.Sprintf("%s/%d/%s%s", fileType, userID, uuidWithoutDashes, extension)
}

// getExtensionFromContentType 从Content-Type获取文件扩展名
func (f *FileLogic) getExtensionFromContentType(contentType string) string {
	switch contentType {
	case "image/jpeg":
		return ".jpg"
	case "image/png":
		return ".png"
	case "image/gif":
		return ".gif"
	case "image/webp":
		return ".webp"
	default:
		return ""
	}
}

// 文件服务相关错误类型

// FileParameterError 文件服务参数错误
type FileParameterError struct {
	Field   string
	Message string
}

func (e *FileParameterError) Error() string {
	return fmt.Sprintf("file parameter error: %s %s", e.Field, e.Message)
}

// UnsupportedFileTypeError 不支持的文件类型错误
type UnsupportedFileTypeError struct {
	ContentType string
}

func (e *UnsupportedFileTypeError) Error() string {
	return fmt.Sprintf("unsupported file type: %s", e.ContentType)
}

// FileStorageError 文件存储错误
type FileStorageError struct {
	Operation string
	Err       error
}

func (e *FileStorageError) Error() string {
	return fmt.Sprintf("file storage error during %s: %v", e.Operation, e.Err)
}

func (e *FileStorageError) Unwrap() error {
	return e.Err
}

// FileNotFoundError 文件未找到错误
type FileNotFoundError struct {
	FileName string
}

func (e *FileNotFoundError) Error() string {
	return fmt.Sprintf("file not found: %s", e.FileName)
}
